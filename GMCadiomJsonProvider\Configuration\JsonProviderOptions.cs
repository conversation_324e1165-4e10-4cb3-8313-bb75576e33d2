namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Base configuration options for JSON providers.
/// </summary>
public abstract class JsonProviderOptions
{
    /// <summary>
    /// Gets or sets the JSON provider type.
    /// </summary>
    public JsonProviderType ProviderType { get; set; }

    /// <summary>
    /// Gets or sets whether to write indented JSON.
    /// </summary>
    public bool WriteIndented { get; set; } = false;

    /// <summary>
    /// Gets or sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    public bool PropertyNameCaseInsensitive { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to allow trailing commas in JSON.
    /// </summary>
    public bool AllowTrailingCommas { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to ignore null values during serialization.
    /// </summary>
    public bool IgnoreNullValues { get; set; } = false;

    /// <summary>
    /// Creates a copy of the current options.
    /// </summary>
    /// <returns>A new instance with the same configuration.</returns>
    public abstract JsonProviderOptions Clone();
}
