namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Maps unified JsonConfiguration to provider-specific options.
/// </summary>
public static class JsonConfigurationMapper
{
    /// <summary>
    /// Converts a unified JsonConfiguration to SystemTextJsonOptions.
    /// </summary>
    /// <param name="config">The unified configuration.</param>
    /// <returns>SystemTextJsonOptions configured according to the unified config.</returns>
    public static SystemTextJsonOptions ToSystemTextJsonOptions(JsonConfiguration config)
    {
        var options = new SystemTextJsonOptions
        {
            WriteIndented = config.WriteIndented,
            PropertyNameCaseInsensitive = config.PropertyNameCaseInsensitive,
            AllowTrailingCommas = config.AllowTrailingCommas,
            IncludeFields = config.IncludeFields,
            MaxDepth = config.MaxDepth,
            ReadCommentHandling = config.AllowComments,
            NumberHandling = config.AllowNumbersFromStrings
        };

        // Map null value handling
        switch (config.NullValueHandling)
        {
            case JsonNullValueHandling.Include:
                options.IgnoreNullValues = false;
                options.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never;
                break;
            case JsonNullValueHandling.Ignore:
            case JsonNullValueHandling.IgnoreWhenWriting:
                options.IgnoreNullValues = true;
                options.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
                break;
        }

        // Map property naming policy
        options.PropertyNamingPolicy = MapNamingPolicyToSystemTextJson(config.PropertyNamingPolicy);
        options.DictionaryKeyPolicy = MapNamingPolicyToSystemTextJson(config.DictionaryKeyNamingPolicy);

        // Map encoder for relaxed escaping
        if (config.UseRelaxedEscaping)
        {
            options.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        }

        return options;
    }

    /// <summary>
    /// Converts a unified JsonConfiguration to NewtonsoftJsonOptions.
    /// </summary>
    /// <param name="config">The unified configuration.</param>
    /// <returns>NewtonsoftJsonOptions configured according to the unified config.</returns>
    public static NewtonsoftJsonOptions ToNewtonsoftJsonOptions(JsonConfiguration config)
    {
        var options = new NewtonsoftJsonOptions
        {
            WriteIndented = config.WriteIndented,
            PropertyNameCaseInsensitive = config.PropertyNameCaseInsensitive,
            AllowTrailingCommas = config.AllowTrailingCommas
        };

        // Map null value handling
        switch (config.NullValueHandling)
        {
            case JsonNullValueHandling.Include:
                options.IgnoreNullValues = false;
                options.NullValueHandling = Newtonsoft.Json.NullValueHandling.Include;
                break;
            case JsonNullValueHandling.Ignore:
            case JsonNullValueHandling.IgnoreWhenWriting:
                options.IgnoreNullValues = true;
                options.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
                break;
        }

        // Map date format handling
        switch (config.DateFormatHandling)
        {
            case JsonDateFormatHandling.IsoDateFormat:
                options.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                break;
            case JsonDateFormatHandling.MicrosoftDateFormat:
                options.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.MicrosoftDateFormat;
                break;
            case JsonDateFormatHandling.UnixTimeStamp:
                // Newtonsoft doesn't have direct Unix timestamp support, use ISO as fallback
                options.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                break;
        }

        // Map missing member handling
        switch (config.MissingMemberHandling)
        {
            case JsonMissingMemberHandling.Ignore:
                options.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Ignore;
                break;
            case JsonMissingMemberHandling.Error:
                options.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Error;
                break;
        }

        // Map reference loop handling
        switch (config.ReferenceLoopHandling)
        {
            case JsonReferenceLoopHandling.Error:
                options.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Error;
                break;
            case JsonReferenceLoopHandling.Ignore:
                options.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                break;
            case JsonReferenceLoopHandling.Serialize:
                options.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Serialize;
                break;
        }

        // Map property naming policy
        options.ContractResolver = MapNamingPolicyToNewtonsoft(config.PropertyNamingPolicy);

        // Map culture
        if (!string.IsNullOrEmpty(config.Culture))
        {
            try
            {
                options.Culture = System.Globalization.CultureInfo.GetCultureInfo(config.Culture);
            }
            catch
            {
                // Ignore invalid culture names
            }
        }

        // Map max depth
        if (config.MaxDepth > 0)
        {
            options.MaxDepth = config.MaxDepth;
        }

        return options;
    }

    /// <summary>
    /// Maps unified naming policy to System.Text.Json naming policy.
    /// </summary>
    private static System.Text.Json.JsonNamingPolicy? MapNamingPolicyToSystemTextJson(JsonNamingPolicy policy)
    {
        return policy switch
        {
            JsonNamingPolicy.CamelCase => System.Text.Json.JsonNamingPolicy.CamelCase,
            JsonNamingPolicy.SnakeCase => System.Text.Json.JsonNamingPolicy.SnakeCaseLower,
            JsonNamingPolicy.KebabCase => System.Text.Json.JsonNamingPolicy.KebabCaseLower,
            _ => null
        };
    }

    /// <summary>
    /// Maps unified naming policy to Newtonsoft.Json contract resolver.
    /// </summary>
    private static Newtonsoft.Json.Serialization.IContractResolver? MapNamingPolicyToNewtonsoft(JsonNamingPolicy policy)
    {
        return policy switch
        {
            JsonNamingPolicy.CamelCase => new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
            JsonNamingPolicy.SnakeCase => new Newtonsoft.Json.Serialization.DefaultContractResolver
            {
                NamingStrategy = new Newtonsoft.Json.Serialization.SnakeCaseNamingStrategy()
            },
            JsonNamingPolicy.KebabCase => new Newtonsoft.Json.Serialization.DefaultContractResolver
            {
                NamingStrategy = new Newtonsoft.Json.Serialization.KebabCaseNamingStrategy()
            },
            _ => null
        };
    }

    /// <summary>
    /// Converts provider-specific options back to unified configuration.
    /// </summary>
    /// <param name="options">The provider-specific options.</param>
    /// <returns>A unified JsonConfiguration.</returns>
    public static JsonConfiguration FromProviderOptions(JsonProviderOptions options)
    {
        var config = new JsonConfiguration
        {
            ProviderType = options.ProviderType,
            WriteIndented = options.WriteIndented,
            PropertyNameCaseInsensitive = options.PropertyNameCaseInsensitive,
            AllowTrailingCommas = options.AllowTrailingCommas
        };

        // Map null value handling
        config.NullValueHandling = options.IgnoreNullValues ? JsonNullValueHandling.Ignore : JsonNullValueHandling.Include;

        // Provider-specific mappings
        if (options is SystemTextJsonOptions systemOptions)
        {
            config.IncludeFields = systemOptions.IncludeFields;
            config.MaxDepth = systemOptions.MaxDepth;
            config.AllowComments = systemOptions.ReadCommentHandling;
            config.AllowNumbersFromStrings = systemOptions.NumberHandling;
            config.UseRelaxedEscaping = systemOptions.Encoder == System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        }
        else if (options is NewtonsoftJsonOptions newtonsoftOptions)
        {
            config.MaxDepth = newtonsoftOptions.MaxDepth ?? 64;
            config.Culture = newtonsoftOptions.Culture?.Name;
        }

        return config;
    }
}
