namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to register JSON providers.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds the JSON provider factory to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProviderFactory(this IServiceCollection services)
    {
        services.AddSingleton<IJsonProviderFactory, JsonProviderFactory>();
        return services;
    }

    /// <summary>
    /// Adds a JSON provider to the service collection using the default Microsoft provider.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the provider options.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, Action<SystemTextJsonOptions>? configure = null)
    {
        return services.AddJsonProvider(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Adds a JSON provider to the service collection with the specified provider type.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="providerType">The type of JSON provider to register.</param>
    /// <param name="configure">Optional action to configure the provider options.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, JsonProviderType providerType, Action<SystemTextJsonOptions>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();

            if (providerType == JsonProviderType.Microsoft && configure != null)
            {
                var options = new SystemTextJsonOptions();
                configure(options);
                return factory.CreateMicrosoftProvider(options);
            }

            return factory.CreateProvider(providerType);
        });

        return services;
    }

    /// <summary>
    /// Adds a Microsoft System.Text.Json provider to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the provider options.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddMicrosoftJsonProvider(this IServiceCollection services, Action<SystemTextJsonOptions>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();

            if (configure != null)
            {
                var options = new SystemTextJsonOptions();
                configure(options);
                return factory.CreateMicrosoftProvider(options);
            }

            return factory.CreateMicrosoftProvider();
        });

        return services;
    }

    /// <summary>
    /// Adds a Newtonsoft.Json provider to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the provider options.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddNewtonsoftJsonProvider(this IServiceCollection services, Action<NewtonsoftJsonOptions>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();

            if (configure != null)
            {
                var options = new NewtonsoftJsonOptions();
                configure(options);
                return factory.CreateNewtonsoftProvider(options);
            }

            return factory.CreateNewtonsoftProvider();
        });

        return services;
    }

    /// <summary>
    /// Adds a JSON provider to the service collection using a builder for fluent configuration.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, Action<IJsonProviderBuilder> configure)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var builder = new JsonProviderBuilder();
            configure(builder);
            return builder.Build();
        });

        return services;
    }

    /// <summary>
    /// Adds a JSON provider to the service collection with specific options.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="options">The configuration options for the JSON provider.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, JsonProviderOptions options)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateProvider(options);
        });

        return services;
    }
}
