using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Configuration options for Newtonsoft.Json provider.
/// </summary>
public class NewtonsoftJsonOptions : JsonProviderOptions
{
    /// <summary>
    /// Initializes a new instance of the <see cref="NewtonsoftJsonOptions"/> class.
    /// </summary>
    public NewtonsoftJsonOptions()
    {
        ProviderType = JsonProviderType.Newtonsoft;
    }

    /// <summary>
    /// Gets or sets the contract resolver.
    /// </summary>
    public IContractResolver? ContractResolver { get; set; }

    /// <summary>
    /// Gets or sets the date format handling.
    /// </summary>
    public DateFormatHandling DateFormatHandling { get; set; } = DateFormatHandling.IsoDateFormat;

    /// <summary>
    /// Gets or sets the date time zone handling.
    /// </summary>
    public DateTimeZoneHandling DateTimeZoneHandling { get; set; } = DateTimeZoneHandling.RoundtripKind;

    /// <summary>
    /// Gets or sets the null value handling.
    /// </summary>
    public NullValueHandling NullValueHandling { get; set; } = NullValueHandling.Include;

    /// <summary>
    /// Gets or sets the default value handling.
    /// </summary>
    public DefaultValueHandling DefaultValueHandling { get; set; } = DefaultValueHandling.Include;

    /// <summary>
    /// Gets or sets the missing member handling.
    /// </summary>
    public MissingMemberHandling MissingMemberHandling { get; set; } = MissingMemberHandling.Ignore;

    /// <summary>
    /// Gets or sets the object creation handling.
    /// </summary>
    public ObjectCreationHandling ObjectCreationHandling { get; set; } = ObjectCreationHandling.Auto;

    /// <summary>
    /// Gets or sets the type name handling.
    /// </summary>
    public TypeNameHandling TypeNameHandling { get; set; } = TypeNameHandling.None;

    /// <summary>
    /// Gets or sets the reference loop handling.
    /// </summary>
    public ReferenceLoopHandling ReferenceLoopHandling { get; set; } = ReferenceLoopHandling.Error;

    /// <summary>
    /// Gets or sets the preserve references handling.
    /// </summary>
    public PreserveReferencesHandling PreserveReferencesHandling { get; set; } = PreserveReferencesHandling.None;

    /// <summary>
    /// Gets or sets the formatting.
    /// </summary>
    public Formatting Formatting { get; set; } = Formatting.None;

    /// <summary>
    /// Gets or sets the culture to use for parsing and formatting.
    /// </summary>
    public System.Globalization.CultureInfo? Culture { get; set; }

    /// <summary>
    /// Gets or sets the maximum depth allowed when reading JSON.
    /// </summary>
    public int? MaxDepth { get; set; }

    /// <summary>
    /// Gets or sets custom converters.
    /// </summary>
    public List<Newtonsoft.Json.JsonConverter> Converters { get; set; } = new();

    /// <summary>
    /// Converts the options to Newtonsoft.Json JsonSerializerSettings.
    /// </summary>
    /// <returns>A configured JsonSerializerSettings instance.</returns>
    public JsonSerializerSettings ToJsonSerializerSettings()
    {
        var settings = new JsonSerializerSettings
        {
            Formatting = WriteIndented ? Formatting.Indented : Formatting,
            ContractResolver = ContractResolver,
            DateFormatHandling = DateFormatHandling,
            DateTimeZoneHandling = DateTimeZoneHandling,
            NullValueHandling = IgnoreNullValues ? Newtonsoft.Json.NullValueHandling.Ignore : NullValueHandling,
            DefaultValueHandling = DefaultValueHandling,
            MissingMemberHandling = MissingMemberHandling,
            ObjectCreationHandling = ObjectCreationHandling,
            TypeNameHandling = TypeNameHandling,
            ReferenceLoopHandling = ReferenceLoopHandling,
            PreserveReferencesHandling = PreserveReferencesHandling,
            Culture = Culture,
            MaxDepth = MaxDepth
        };

        foreach (var converter in Converters)
        {
            settings.Converters.Add(converter);
        }

        return settings;
    }

    /// <summary>
    /// Creates a copy of the current options.
    /// </summary>
    /// <returns>A new instance with the same configuration.</returns>
    public override JsonProviderOptions Clone()
    {
        return new NewtonsoftJsonOptions
        {
            ProviderType = ProviderType,
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            IgnoreNullValues = IgnoreNullValues,
            ContractResolver = ContractResolver,
            DateFormatHandling = DateFormatHandling,
            DateTimeZoneHandling = DateTimeZoneHandling,
            NullValueHandling = NullValueHandling,
            DefaultValueHandling = DefaultValueHandling,
            MissingMemberHandling = MissingMemberHandling,
            ObjectCreationHandling = ObjectCreationHandling,
            TypeNameHandling = TypeNameHandling,
            ReferenceLoopHandling = ReferenceLoopHandling,
            PreserveReferencesHandling = PreserveReferencesHandling,
            Formatting = Formatting,
            Culture = Culture,
            MaxDepth = MaxDepth,
            Converters = new List<Newtonsoft.Json.JsonConverter>(Converters)
        };
    }
}
