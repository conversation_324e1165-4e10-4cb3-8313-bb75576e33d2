namespace GMCadiomJsonProvider.Factory;

/// <summary>
/// Defines the contract for creating JSON providers.
/// </summary>
public interface IJsonProviderFactory
{
    /// <summary>
    /// Creates a JSON provider of the specified type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to create.</param>
    /// <returns>A JSON provider instance.</returns>
    IJsonProvider CreateProvider(JsonProviderType providerType);

    /// <summary>
    /// Creates a Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="options">Optional configuration options.</param>
    /// <returns>A Microsoft JSON provider instance.</returns>
    IJsonProvider CreateMicrosoftProvider(SystemTextJsonOptions? options = null);

    /// <summary>
    /// Creates a Newtonsoft.Json provider.
    /// </summary>
    /// <param name="options">Optional configuration options.</param>
    /// <returns>A Newtonsoft JSON provider instance.</returns>
    IJsonProvider CreateNewtonsoftProvider(NewtonsoftJsonOptions? options = null);

    /// <summary>
    /// Creates a JSON provider with the specified configuration.
    /// </summary>
    /// <param name="options">The configuration options.</param>
    /// <returns>A JSON provider instance.</returns>
    IJsonProvider CreateProvider(JsonProviderOptions options);

    /// <summary>
    /// Creates a JSON provider using unified configuration.
    /// </summary>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>A JSON provider instance.</returns>
    IJsonProvider CreateProvider(JsonConfiguration config);

    /// <summary>
    /// Creates a JSON provider from a JSON configuration string.
    /// </summary>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>A JSON provider instance.</returns>
    IJsonProvider CreateProviderFromJson(string jsonConfig);
}
