using GMCadiomJsonProvider.Abstraction;
using GMCadiomJsonProvider.Builder;
using GMCadiomJsonProvider.Tests.TestModels;

namespace GMCadiomJsonProvider.Tests;

public class JsonProviderBuilderTests
{
    private readonly TestPerson _testPerson;

    public JsonProviderBuilderTests()
    {
        _testPerson = new TestPerson
        {
            Id = 1,
            Name = "<PERSON> Do<PERSON>",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true
        };
    }

    [Fact]
    public void Build_WithDefaultSettings_ShouldCreateMicrosoftProvider()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder.Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void UseMicrosoft_ShouldCreateMicrosoftProvider()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseMicrosoft()
            .Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        provider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void UseNewtonsoft_ShouldCreateNewtonsoftProvider()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseNewtonsoft()
            .Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        provider.ProviderName.Should().Be("Newtonsoft");
    }

    [Fact]
    public void UseProvider_WithMicrosoft_ShouldCreateMicrosoftProvider()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseProvider(JsonProviderType.Microsoft)
            .Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void UseProvider_WithNewtonsoft_ShouldCreateNewtonsoftProvider()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseProvider(JsonProviderType.Newtonsoft)
            .Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void WithIndentation_ShouldConfigureIndentedOutput()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .WithIndentation(true)
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        json.Should().Contain("\n");
        json.Should().Contain("  ");
    }

    [Fact]
    public void WithIgnoreNullValues_ShouldIgnoreNullProperties()
    {
        // Arrange
        var builder = new JsonProviderBuilder();
        var personWithNulls = new TestPerson
        {
            Id = 1,
            Name = "John Doe",
            Email = null, // This should be ignored
            Salary = null // This should be ignored
        };

        // Act
        var provider = builder
            .WithIgnoreNullValues(true)
            .Build();

        var json = provider.Serialize(personWithNulls);

        // Assert
        json.Should().NotContain("Email");
        json.Should().NotContain("Salary");
        json.Should().Contain("John Doe");
    }

    [Fact]
    public void FluentConfiguration_ShouldChainMethods()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseMicrosoft()
            .WithIndentation(true)
            .WithCaseInsensitiveProperties(true)
            .WithTrailingCommas(true)
            .WithIgnoreNullValues(true)
            .Build();

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void UseMicrosoft_WithConfiguration_ShouldApplyOptions()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseMicrosoft(options =>
            {
                options.WriteIndented = true;
                options.PropertyNameCaseInsensitive = true;
            })
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void UseNewtonsoft_WithConfiguration_ShouldApplyOptions()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act
        var provider = builder
            .UseNewtonsoft(options =>
            {
                options.WriteIndented = true;
                options.PropertyNameCaseInsensitive = true;
            })
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.Should().NotBeNull();
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
    }

    [Fact]
    public void ConfigureOptions_ShouldApplyToBothProviders()
    {
        // Arrange
        var builder = new JsonProviderBuilder();

        // Act & Assert - Microsoft
        var microsoftProvider = builder
            .UseMicrosoft()
            .ConfigureOptions(options => options.WriteIndented = true)
            .Build();

        var microsoftJson = microsoftProvider.Serialize(_testPerson);
        microsoftJson.Should().Contain("\n");

        // Act & Assert - Newtonsoft
        var newtonsoftProvider = builder
            .UseNewtonsoft()
            .ConfigureOptions(options => options.WriteIndented = true)
            .Build();

        var newtonsoftJson = newtonsoftProvider.Serialize(_testPerson);
        newtonsoftJson.Should().Contain("\n");
    }
}
