﻿# GMCadiomJsonProvider

A flexible JSON serialization/deserialization provider library for .NET 8+ that supports both System.Text.Json and Newtonsoft.Json with fluent configuration, dependency injection, and factory patterns.

## Features

- **Multiple JSON Providers**: Support for both Microsoft System.Text.Json and Newtonsoft.Json
- **Fluent Configuration**: Builder pattern for easy and readable configuration
- **Factory Pattern**: Create providers using factory methods
- **Dependency Injection**: Built-in support for Microsoft.Extensions.DependencyInjection
- **Static Helpers**: Convenient static methods for quick usage
- **Async Support**: Full async/await support for all operations
- **Stream Support**: Direct serialization to/from streams
- **Type Safety**: Generic and non-generic overloads for flexibility
- **Comprehensive Configuration**: Extensive options for both providers

## Installation

```bash
dotnet add package GMCadiomJsonProvider
```

## Quick Start

### Using Static Helper Methods

```csharp
using GMCadiomJsonProvider;

// Serialize using default provider (Microsoft System.Text.Json)
var person = new Person { Name = "<PERSON>", Age = 30 };
var json = GMCadiomJson.Serialize(person);

// Deserialize
var deserializedPerson = GMCadiomJson.Deserialize<Person>(json);

// Async operations
var jsonAsync = await GMCadiomJson.SerializeAsync(person);
var personAsync = await GMCadiomJson.DeserializeAsync<Person>(jsonAsync);
```

### Using Builder Pattern

```csharp
using GMCadiomJsonProvider;

// Create a configured provider using fluent builder
var provider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft()
    .WithIndentation(true)
    .WithIgnoreNullValues(true)
    .WithCaseInsensitiveProperties(true)
    .Build();

var json = provider.Serialize(person);
```

### Using Factory Pattern

```csharp
using GMCadiomJsonProvider;

// Create providers using factory
var microsoftProvider = GMCadiomJson.Factory.CreateMicrosoftProvider();
var newtonsoftProvider = GMCadiomJson.Factory.CreateNewtonsoftProvider();

// With configuration
var configuredProvider = GMCadiomJson.CreateMicrosoft(options =>
{
    options.WriteIndented = true;
    options.PropertyNameCaseInsensitive = true;
});
```

## Dependency Injection

### Basic Registration

```csharp
using GMCadiomJsonProvider.Extensions;

// Register default provider (Microsoft System.Text.Json)
services.AddJsonProvider();

// Register specific provider
services.AddMicrosoftJsonProvider();
services.AddNewtonsoftJsonProvider();
```

### With Configuration

```csharp
// Configure Microsoft provider
services.AddMicrosoftJsonProvider(options =>
{
    options.WriteIndented = true;
    options.PropertyNameCaseInsensitive = true;
    options.IgnoreNullValues = true;
});

// Configure Newtonsoft provider
services.AddNewtonsoftJsonProvider(options =>
{
    options.WriteIndented = true;
    options.NullValueHandling = NullValueHandling.Ignore;
});
```

### Using Builder in DI

```csharp
services.AddJsonProvider(builder =>
{
    builder.UseNewtonsoft()
           .WithIndentation(true)
           .WithIgnoreNullValues(true);
});
```

### Injecting and Using

```csharp
public class MyService
{
    private readonly IJsonProvider _jsonProvider;

    public MyService(IJsonProvider jsonProvider)
    {
        _jsonProvider = jsonProvider;
    }

    public async Task<string> SerializeDataAsync<T>(T data)
    {
        return await _jsonProvider.SerializeAsync(data);
    }
}
```

## Configuration Options

### Common Options (Both Providers)

```csharp
var options = new SystemTextJsonOptions // or NewtonsoftJsonOptions
{
    WriteIndented = true,                    // Pretty-print JSON
    PropertyNameCaseInsensitive = true,      // Case-insensitive deserialization
    AllowTrailingCommas = true,              // Allow trailing commas in JSON
    IgnoreNullValues = true                  // Ignore null values during serialization
};
```

### System.Text.Json Specific Options

```csharp
var microsoftOptions = new SystemTextJsonOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    IncludeFields = true,
    MaxDepth = 128,
    ReadCommentHandling = true,
    NumberHandling = true,
    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
};

// Add custom converters
microsoftOptions.Converters.Add(new JsonStringEnumConverter());
```

### Newtonsoft.Json Specific Options

```csharp
var newtonsoftOptions = new NewtonsoftJsonOptions
{
    ContractResolver = new CamelCasePropertyNamesContractResolver(),
    DateFormatHandling = DateFormatHandling.IsoDateFormat,
    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
    NullValueHandling = NullValueHandling.Ignore,
    DefaultValueHandling = DefaultValueHandling.Ignore,
    MissingMemberHandling = MissingMemberHandling.Ignore,
    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
    TypeNameHandling = TypeNameHandling.None,
    MaxDepth = 128
};

// Add custom converters
newtonsoftOptions.Converters.Add(new StringEnumConverter());
```

## Advanced Usage

### Stream Operations

```csharp
// Serialize to stream
using var stream = new MemoryStream();
provider.SerializeToStream(stream, person);

// Deserialize from stream
stream.Position = 0;
var deserializedPerson = provider.DeserializeFromStream<Person>(stream);

// Async stream operations
using var asyncStream = new MemoryStream();
await provider.SerializeToStreamAsync(asyncStream, person);

asyncStream.Position = 0;
var asyncPerson = await provider.DeserializeFromStreamAsync<Person>(asyncStream);
```

### Non-Generic Operations

```csharp
// Serialize and deserialize using Type parameter
var json = provider.Serialize(person);
var deserializedObject = provider.Deserialize(json, typeof(Person));
var typedPerson = (Person)deserializedObject;

// Stream operations with Type
using var stream = new MemoryStream();
provider.SerializeToStream(stream, person);
stream.Position = 0;
var streamObject = provider.DeserializeFromStream(stream, typeof(Person));
```

### Provider Comparison

```csharp
// Create both providers for comparison
var microsoftProvider = GMCadiomJson.CreateMicrosoft();
var newtonsoftProvider = GMCadiomJson.CreateNewtonsoft();

// Serialize with both
var microsoftJson = microsoftProvider.Serialize(person);
var newtonsoftJson = newtonsoftProvider.Serialize(person);

Console.WriteLine($"Microsoft: {microsoftProvider.ProviderName}");
Console.WriteLine($"Newtonsoft: {newtonsoftProvider.ProviderName}");
```

## Best Practices

### 1. Choose the Right Provider

- **System.Text.Json (Microsoft)**:
  - Better performance and lower memory allocation
  - Built into .NET, no additional dependencies
  - Recommended for new applications
  - Limited customization compared to Newtonsoft

- **Newtonsoft.Json**:
  - More features and customization options
  - Better support for complex scenarios
  - Widely used in existing applications
  - Slightly slower than System.Text.Json

### 2. Configuration Guidelines

```csharp
// For APIs - use camelCase naming
var apiProvider = GMCadiomJson.CreateBuilder()
    .UseMicrosoft(options =>
    {
        options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.WriteIndented = false; // Compact for APIs
    })
    .Build();

// For configuration files - use indentation
var configProvider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft(options =>
    {
        options.WriteIndented = true;
        options.NullValueHandling = NullValueHandling.Ignore;
    })
    .Build();
```

### 3. Dependency Injection Best Practices

```csharp
// Register as singleton for better performance
services.AddSingleton<IJsonProvider>(serviceProvider =>
{
    return GMCadiomJson.CreateBuilder()
        .UseMicrosoft()
        .WithIndentation(false)
        .WithIgnoreNullValues(true)
        .Build();
});

// Or use the built-in extension methods
services.AddMicrosoftJsonProvider(options =>
{
    options.WriteIndented = false;
    options.IgnoreNullValues = true;
});
```

### 4. Error Handling

```csharp
try
{
    var json = provider.Serialize(complexObject);
    var deserialized = provider.Deserialize<ComplexObject>(json);
}
catch (JsonException ex)
{
    // Handle JSON-specific errors
    Console.WriteLine($"JSON Error: {ex.Message}");
}
catch (Exception ex)
{
    // Handle other errors
    Console.WriteLine($"General Error: {ex.Message}");
}
```

## API Reference

### IJsonProvider Interface

```csharp
public interface IJsonProvider
{
    JsonProviderType ProviderType { get; }
    string ProviderName { get; }

    // Synchronous methods
    string Serialize<T>(T value);
    T? Deserialize<T>(string json);
    object? Deserialize(string json, Type type);

    // Asynchronous methods
    Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default);
    Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default);

    // Stream methods
    void SerializeToStream<T>(Stream stream, T value);
    T? DeserializeFromStream<T>(Stream stream);
    object? DeserializeFromStream(Stream stream, Type type);

    // Async stream methods
    Task SerializeToStreamAsync<T>(Stream stream, T value, CancellationToken cancellationToken = default);
    Task<T?> DeserializeFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken = default);
}
```

### Static Helper Methods

```csharp
public static class GMCadiomJson
{
    // Factory and builders
    public static IJsonProviderFactory Factory { get; }
    public static IJsonProvider Default { get; }
    public static IJsonProviderBuilder CreateBuilder();

    // Provider creation
    public static IJsonProvider CreateDefault();
    public static IJsonProvider CreateMicrosoft(Action<SystemTextJsonOptions>? configure = null);
    public static IJsonProvider CreateNewtonsoft(Action<NewtonsoftJsonOptions>? configure = null);
    public static IJsonProvider CreateProvider(JsonProviderType providerType);

    // Convenience methods using default provider
    public static string Serialize<T>(T value);
    public static Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default);
    public static T? Deserialize<T>(string json);
    public static Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default);
    public static object? Deserialize(string json, Type type);
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.