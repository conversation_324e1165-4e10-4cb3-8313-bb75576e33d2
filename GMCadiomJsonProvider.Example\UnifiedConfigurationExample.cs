using GMCadiomJsonProvider;
using GMCadiomJsonProvider.Abstraction;
using GMCadiomJsonProvider.Configuration;

namespace GMCadiomJsonProvider.Example;

public static class UnifiedConfigurationExample
{
    public static void RunExample()
    {
        Console.WriteLine("=== Unified Configuration Example ===\n");

        var person = new Person
        {
            Id = 1,
            Name = "John Doe",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true,
            Salary = null, // Test null handling
            Tags = new List<string> { "developer", "senior" },
            Address = new Address
            {
                Street = "123 Main St",
                City = "New York",
                Country = "USA",
                PostalCode = "10001"
            }
        };

        Console.WriteLine("1. Using Unified Configuration Object:");
        Console.WriteLine("=====================================");

        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore,
            AllowComments = true,
            MaxDepth = 128
        };

        var provider = GMCadiomJson.CreateProvider(config);
        var json = provider.Serialize(person);

        Console.WriteLine($"Provider: {provider.ProviderName}");
        Console.WriteLine($"JSON Output:\n{json}\n");

        Console.WriteLine("2. Using Configuration Action:");
        Console.WriteLine("=============================");

        var actionProvider = GMCadiomJson.CreateProvider(cfg =>
        {
            cfg.ProviderType = JsonProviderType.Newtonsoft;
            cfg.WriteIndented = true;
            cfg.PropertyNamingPolicy = JsonNamingPolicy.SnakeCase;
            cfg.NullValueHandling = JsonNullValueHandling.Ignore;
            cfg.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
        });

        var actionJson = actionProvider.Serialize(person);
        Console.WriteLine($"Provider: {actionProvider.ProviderName}");
        Console.WriteLine($"JSON Output:\n{actionJson}\n");

        Console.WriteLine("3. Using Builder with Unified Configuration:");
        Console.WriteLine("===========================================");

        var builderProvider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(cfg =>
            {
                cfg.ProviderType = JsonProviderType.Microsoft;
                cfg.WriteIndented = true;
                cfg.PropertyNamingPolicy = JsonNamingPolicy.KebabCase;
                cfg.NullValueHandling = JsonNullValueHandling.Ignore;
                cfg.UseRelaxedEscaping = true;
            })
            .Build();

        var builderJson = builderProvider.Serialize(person);
        Console.WriteLine($"Provider: {builderProvider.ProviderName}");
        Console.WriteLine($"JSON Output:\n{builderJson}\n");

        Console.WriteLine("4. Using JSON Configuration String:");
        Console.WriteLine("==================================");

        var jsonConfigString = """
        {
            "providerType": 1,
            "writeIndented": true,
            "propertyNamingPolicy": 1,
            "nullValueHandling": 1,
            "dateFormatHandling": 0,
            "maxDepth": 64,
            "allowComments": false
        }
        """;

        var jsonConfigProvider = GMCadiomJson.CreateProviderFromJson(jsonConfigString);
        var jsonConfigJson = jsonConfigProvider.Serialize(person);

        Console.WriteLine($"Provider: {jsonConfigProvider.ProviderName}");
        Console.WriteLine($"JSON Output:\n{jsonConfigJson}\n");

        Console.WriteLine("5. Configuration Serialization/Deserialization:");
        Console.WriteLine("===============================================");

        var originalConfig = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore,
            Culture = "en-US",
            MaxDepth = 256
        };

        // Serialize configuration to JSON
        var configJson = originalConfig.ToJson();
        Console.WriteLine($"Configuration as JSON:\n{configJson}\n");

        // Deserialize configuration from JSON
        var deserializedConfig = JsonConfiguration.FromJson(configJson);
        var deserializedProvider = GMCadiomJson.CreateProvider(deserializedConfig);

        Console.WriteLine($"Deserialized Provider: {deserializedProvider.ProviderName}");
        Console.WriteLine($"Configurations match: {originalConfig.ProviderType == deserializedConfig.ProviderType}");

        Console.WriteLine("\n6. Comparing Same Configuration with Different Providers:");
        Console.WriteLine("========================================================");

        var sharedConfig = new JsonConfiguration
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore
        };

        // Microsoft provider
        sharedConfig.ProviderType = JsonProviderType.Microsoft;
        var microsoftProvider = GMCadiomJson.CreateProvider(sharedConfig);
        var microsoftJson = microsoftProvider.Serialize(person);

        // Newtonsoft provider
        sharedConfig.ProviderType = JsonProviderType.Newtonsoft;
        var newtonsoftProvider = GMCadiomJson.CreateProvider(sharedConfig);
        var newtonsoftJson = newtonsoftProvider.Serialize(person);

        Console.WriteLine("Microsoft Provider Output:");
        Console.WriteLine(microsoftJson);
        Console.WriteLine("\nNewtonsoft Provider Output:");
        Console.WriteLine(newtonsoftJson);

        Console.WriteLine("\n7. Configuration Mapping Back and Forth:");
        Console.WriteLine("========================================");

        var systemOptions = JsonConfigurationMapper.ToSystemTextJsonOptions(originalConfig);
        var mappedBackConfig = JsonConfigurationMapper.FromProviderOptions(systemOptions);

        Console.WriteLine($"Original Provider Type: {originalConfig.ProviderType}");
        Console.WriteLine($"Mapped Back Provider Type: {mappedBackConfig.ProviderType}");
        Console.WriteLine($"Original WriteIndented: {originalConfig.WriteIndented}");
        Console.WriteLine($"Mapped Back WriteIndented: {mappedBackConfig.WriteIndented}");

        Console.WriteLine("\n=== Unified Configuration Example Complete ===");
    }
}
