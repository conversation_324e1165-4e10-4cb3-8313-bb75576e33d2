using System.Text.Json;

namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Configuration options for System.Text.Json provider.
/// </summary>
public class SystemTextJsonOptions : JsonProviderOptions
{
    /// <summary>
    /// Initializes a new instance of the <see cref="SystemTextJsonOptions"/> class.
    /// </summary>
    public SystemTextJsonOptions()
    {
        ProviderType = JsonProviderType.Microsoft;
    }

    /// <summary>
    /// Gets or sets the property naming policy.
    /// </summary>
    public System.Text.Json.JsonNamingPolicy? PropertyNamingPolicy { get; set; }

    /// <summary>
    /// Gets or sets the dictionary key naming policy.
    /// </summary>
    public System.Text.Json.JsonNamingPolicy? DictionaryKeyPolicy { get; set; }

    /// <summary>
    /// Gets or sets the default ignore condition for properties.
    /// </summary>
    public System.Text.Json.Serialization.JsonIgnoreCondition DefaultIgnoreCondition { get; set; } = System.Text.Json.Serialization.JsonIgnoreCondition.Never;

    /// <summary>
    /// Gets or sets whether to include fields during serialization and deserialization.
    /// </summary>
    public bool IncludeFields { get; set; } = false;

    /// <summary>
    /// Gets or sets the maximum depth allowed when reading JSON.
    /// </summary>
    public int MaxDepth { get; set; } = 64;

    /// <summary>
    /// Gets or sets whether to allow reading comments in JSON.
    /// </summary>
    public bool ReadCommentHandling { get; set; } = false;

    /// <summary>
    /// Gets or sets whether numbers can be read from strings.
    /// </summary>
    public bool NumberHandling { get; set; } = false;

    /// <summary>
    /// Gets or sets the encoder to use for escaping strings.
    /// </summary>
    public System.Text.Encodings.Web.JavaScriptEncoder? Encoder { get; set; }

    /// <summary>
    /// Gets or sets custom converters.
    /// </summary>
    public List<System.Text.Json.Serialization.JsonConverter> Converters { get; set; } = new();

    /// <summary>
    /// Converts the options to System.Text.Json JsonSerializerOptions.
    /// </summary>
    /// <returns>A configured JsonSerializerOptions instance.</returns>
    public JsonSerializerOptions ToJsonSerializerOptions()
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            PropertyNamingPolicy = PropertyNamingPolicy,
            DictionaryKeyPolicy = DictionaryKeyPolicy,
            DefaultIgnoreCondition = IgnoreNullValues ? System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull : DefaultIgnoreCondition,
            IncludeFields = IncludeFields,
            MaxDepth = MaxDepth
        };

        if (ReadCommentHandling)
        {
            options.ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip;
        }

        if (NumberHandling)
        {
            options.NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
        }

        if (Encoder != null)
        {
            options.Encoder = Encoder;
        }

        foreach (var converter in Converters)
        {
            options.Converters.Add(converter);
        }

        return options;
    }

    /// <summary>
    /// Creates a copy of the current options.
    /// </summary>
    /// <returns>A new instance with the same configuration.</returns>
    public override JsonProviderOptions Clone()
    {
        return new SystemTextJsonOptions
        {
            ProviderType = ProviderType,
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            IgnoreNullValues = IgnoreNullValues,
            PropertyNamingPolicy = PropertyNamingPolicy,
            DictionaryKeyPolicy = DictionaryKeyPolicy,
            DefaultIgnoreCondition = DefaultIgnoreCondition,
            IncludeFields = IncludeFields,
            MaxDepth = MaxDepth,
            ReadCommentHandling = ReadCommentHandling,
            NumberHandling = NumberHandling,
            Encoder = Encoder,
            Converters = new List<System.Text.Json.Serialization.JsonConverter>(Converters)
        };
    }
}
