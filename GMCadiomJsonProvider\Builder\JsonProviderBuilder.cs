namespace GMCadiomJsonProvider.Builder;

/// <summary>
/// Builder for creating JSON providers with fluent configuration.
/// </summary>
public class JsonProviderBuilder : IJsonProviderBuilder
{
    private JsonProviderType _providerType = JsonProviderType.Microsoft;
    private SystemTextJsonOptions _microsoftOptions = new();
    private NewtonsoftJsonOptions _newtonsoftOptions = new();

    /// <summary>
    /// Configures the builder to use Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure System.Text.Json options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseMicrosoft(Action<SystemTextJsonOptions>? configure = null)
    {
        _providerType = JsonProviderType.Microsoft;
        configure?.Invoke(_microsoftOptions);
        return this;
    }

    /// <summary>
    /// Configures the builder to use Newtonsoft.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure Newtonsoft.Json options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseNewtonsoft(Action<NewtonsoftJsonOptions>? configure = null)
    {
        _providerType = JsonProviderType.Newtonsoft;
        configure?.Invoke(_newtonsoftOptions);
        return this;
    }

    /// <summary>
    /// Configures the builder to use a specific provider type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to use.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseProvider(JsonProviderType providerType)
    {
        _providerType = providerType;
        return this;
    }

    /// <summary>
    /// Configures common JSON options.
    /// </summary>
    /// <param name="configure">Action to configure common options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder ConfigureOptions(Action<JsonProviderOptions> configure)
    {
        configure(_microsoftOptions);
        configure(_newtonsoftOptions);
        return this;
    }

    /// <summary>
    /// Sets whether to write indented JSON.
    /// </summary>
    /// <param name="writeIndented">True to write indented JSON; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithIndentation(bool writeIndented = true)
    {
        _microsoftOptions.WriteIndented = writeIndented;
        _newtonsoftOptions.WriteIndented = writeIndented;
        return this;
    }

    /// <summary>
    /// Sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    /// <param name="caseInsensitive">True for case-insensitive property names; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithCaseInsensitiveProperties(bool caseInsensitive = true)
    {
        _microsoftOptions.PropertyNameCaseInsensitive = caseInsensitive;
        _newtonsoftOptions.PropertyNameCaseInsensitive = caseInsensitive;
        return this;
    }

    /// <summary>
    /// Sets whether to allow trailing commas in JSON.
    /// </summary>
    /// <param name="allowTrailingCommas">True to allow trailing commas; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithTrailingCommas(bool allowTrailingCommas = true)
    {
        _microsoftOptions.AllowTrailingCommas = allowTrailingCommas;
        _newtonsoftOptions.AllowTrailingCommas = allowTrailingCommas;
        return this;
    }

    /// <summary>
    /// Sets whether to ignore null values during serialization.
    /// </summary>
    /// <param name="ignoreNullValues">True to ignore null values; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithIgnoreNullValues(bool ignoreNullValues = true)
    {
        _microsoftOptions.IgnoreNullValues = ignoreNullValues;
        _newtonsoftOptions.IgnoreNullValues = ignoreNullValues;
        return this;
    }

    /// <summary>
    /// Builds the configured JSON provider.
    /// </summary>
    /// <returns>A configured JSON provider instance.</returns>
    public IJsonProvider Build()
    {
        return _providerType switch
        {
            JsonProviderType.Microsoft => new SystemTextJsonProvider(_microsoftOptions),
            JsonProviderType.Newtonsoft => new NewtonsoftJsonProvider(_newtonsoftOptions),
            _ => throw new ArgumentOutOfRangeException(nameof(_providerType), _providerType, "Unsupported provider type.")
        };
    }
}
