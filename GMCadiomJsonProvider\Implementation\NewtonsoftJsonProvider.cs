using GMCadiomJsonProvider.Abstraction;
using GMCadiomJsonProvider.Configuration;
using Newtonsoft.Json;

namespace GMCadiomJsonProvider.Implementation;

/// <summary>
/// JSON provider implementation using Newtonsoft.Json.
/// </summary>
public class NewtonsoftJsonProvider : IJsonProvider
{
    private readonly JsonSerializerSettings _settings;

    /// <summary>
    /// Initializes a new instance of the <see cref="NewtonsoftJsonProvider"/> class.
    /// </summary>
    /// <param name="options">The configuration options for Newtonsoft.Json.</param>
    public NewtonsoftJsonProvider(NewtonsoftJsonOptions? options = null)
    {
        var config = options ?? new NewtonsoftJsonOptions();
        _settings = config.ToJsonSerializerSettings();
    }

    /// <summary>
    /// Gets the type of the JSON provider.
    /// </summary>
    public JsonProviderType ProviderType => JsonProviderType.Newtonsoft;

    /// <summary>
    /// Gets the name of the JSON provider.
    /// </summary>
    public string ProviderName => "Newtonsoft";

    /// <summary>
    /// Serializes an object to a JSON string.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <returns>A JSON string representation of the object.</returns>
    public string Serialize<T>(T value)
    {
        return JsonConvert.SerializeObject(value, _settings);
    }

    /// <summary>
    /// Serializes an object to a JSON string asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a JSON string representation of the object.</returns>
    public Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(JsonConvert.SerializeObject(value, _settings));
    }

    /// <summary>
    /// Serializes an object to a stream.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    public void SerializeToStream<T>(Stream stream, T value)
    {
        using var writer = new StreamWriter(stream, leaveOpen: true);
        using var jsonWriter = new JsonTextWriter(writer);
        var serializer = JsonSerializer.Create(_settings);
        serializer.Serialize(jsonWriter, value);
    }

    /// <summary>
    /// Serializes an object to a stream asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task SerializeToStreamAsync<T>(Stream stream, T value, CancellationToken cancellationToken = default)
    {
        using var writer = new StreamWriter(stream, leaveOpen: true);
        using var jsonWriter = new JsonTextWriter(writer);
        var serializer = JsonSerializer.Create(_settings);
        
        await Task.Run(() => serializer.Serialize(jsonWriter, value), cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <returns>The deserialized object.</returns>
    public T? Deserialize<T>(string json)
    {
        return JsonConvert.DeserializeObject<T>(json, _settings);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    public Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(JsonConvert.DeserializeObject<T>(json, _settings));
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <returns>The deserialized object.</returns>
    public T? DeserializeFromStream<T>(Stream stream)
    {
        using var reader = new StreamReader(stream, leaveOpen: true);
        using var jsonReader = new JsonTextReader(reader);
        var serializer = JsonSerializer.Create(_settings);
        return serializer.Deserialize<T>(jsonReader);
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    public async Task<T?> DeserializeFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken = default)
    {
        using var reader = new StreamReader(stream, leaveOpen: true);
        using var jsonReader = new JsonTextReader(reader);
        var serializer = JsonSerializer.Create(_settings);
        
        return await Task.Run(() => serializer.Deserialize<T>(jsonReader), cancellationToken);
    }

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    public object? Deserialize(string json, Type type)
    {
        return JsonConvert.DeserializeObject(json, type, _settings);
    }

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    public object? DeserializeFromStream(Stream stream, Type type)
    {
        using var reader = new StreamReader(stream, leaveOpen: true);
        using var jsonReader = new JsonTextReader(reader);
        var serializer = JsonSerializer.Create(_settings);
        return serializer.Deserialize(jsonReader, type);
    }
}
