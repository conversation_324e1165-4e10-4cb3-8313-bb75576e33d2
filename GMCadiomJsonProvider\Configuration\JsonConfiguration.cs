using GMCadiomJsonProvider.Abstraction;

namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Unified configuration for JSON providers that maps to provider-specific settings.
/// </summary>
public class JsonConfiguration
{
    /// <summary>
    /// Gets or sets the JSON provider type to use.
    /// </summary>
    public JsonProviderType ProviderType { get; set; } = JsonProviderType.Microsoft;

    /// <summary>
    /// Gets or sets whether to write indented (pretty-printed) JSON.
    /// </summary>
    public bool WriteIndented { get; set; } = false;

    /// <summary>
    /// Gets or sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    public bool PropertyNameCaseInsensitive { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to allow trailing commas in JSON.
    /// </summary>
    public bool AllowTrailingCommas { get; set; } = false;

    /// <summary>
    /// Gets or sets how null values should be handled during serialization.
    /// </summary>
    public JsonNullValueHandling NullValueHandling { get; set; } = JsonNullValueHandling.Include;

    /// <summary>
    /// Gets or sets the naming policy for JSON properties.
    /// </summary>
    public JsonNamingPolicy PropertyNamingPolicy { get; set; } = JsonNamingPolicy.Default;

    /// <summary>
    /// Gets or sets the naming policy for dictionary keys.
    /// </summary>
    public JsonNamingPolicy DictionaryKeyNamingPolicy { get; set; } = JsonNamingPolicy.Default;

    /// <summary>
    /// Gets or sets how dates should be formatted in JSON.
    /// </summary>
    public JsonDateFormatHandling DateFormatHandling { get; set; } = JsonDateFormatHandling.IsoDateFormat;

    /// <summary>
    /// Gets or sets how missing members should be handled during deserialization.
    /// </summary>
    public JsonMissingMemberHandling MissingMemberHandling { get; set; } = JsonMissingMemberHandling.Ignore;

    /// <summary>
    /// Gets or sets how reference loops should be handled.
    /// </summary>
    public JsonReferenceLoopHandling ReferenceLoopHandling { get; set; } = JsonReferenceLoopHandling.Error;

    /// <summary>
    /// Gets or sets whether to include fields during serialization and deserialization.
    /// </summary>
    public bool IncludeFields { get; set; } = false;

    /// <summary>
    /// Gets or sets the maximum depth allowed when reading JSON.
    /// </summary>
    public int MaxDepth { get; set; } = 64;

    /// <summary>
    /// Gets or sets whether to allow reading comments in JSON.
    /// </summary>
    public bool AllowComments { get; set; } = false;

    /// <summary>
    /// Gets or sets whether numbers can be read from strings.
    /// </summary>
    public bool AllowNumbersFromStrings { get; set; } = false;

    /// <summary>
    /// Gets or sets the culture to use for parsing and formatting.
    /// </summary>
    public string? Culture { get; set; }

    /// <summary>
    /// Gets or sets custom date format string.
    /// </summary>
    public string? CustomDateFormat { get; set; }

    /// <summary>
    /// Gets or sets whether to use relaxed JSON escaping.
    /// </summary>
    public bool UseRelaxedEscaping { get; set; } = false;

    /// <summary>
    /// Creates a copy of the current configuration.
    /// </summary>
    /// <returns>A new instance with the same configuration.</returns>
    public JsonConfiguration Clone()
    {
        return new JsonConfiguration
        {
            ProviderType = ProviderType,
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            NullValueHandling = NullValueHandling,
            PropertyNamingPolicy = PropertyNamingPolicy,
            DictionaryKeyNamingPolicy = DictionaryKeyNamingPolicy,
            DateFormatHandling = DateFormatHandling,
            MissingMemberHandling = MissingMemberHandling,
            ReferenceLoopHandling = ReferenceLoopHandling,
            IncludeFields = IncludeFields,
            MaxDepth = MaxDepth,
            AllowComments = AllowComments,
            AllowNumbersFromStrings = AllowNumbersFromStrings,
            Culture = Culture,
            CustomDateFormat = CustomDateFormat,
            UseRelaxedEscaping = UseRelaxedEscaping
        };
    }

    /// <summary>
    /// Creates a JsonConfiguration from a JSON string.
    /// </summary>
    /// <param name="json">The JSON configuration string.</param>
    /// <returns>A JsonConfiguration instance.</returns>
    public static JsonConfiguration FromJson(string json)
    {
        var options = new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
        return System.Text.Json.JsonSerializer.Deserialize<JsonConfiguration>(json, options) ?? new JsonConfiguration();
    }

    /// <summary>
    /// Converts the configuration to a JSON string.
    /// </summary>
    /// <param name="writeIndented">Whether to write indented JSON.</param>
    /// <returns>A JSON representation of the configuration.</returns>
    public string ToJson(bool writeIndented = true)
    {
        var options = new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = writeIndented,
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
        return System.Text.Json.JsonSerializer.Serialize(this, options);
    }
}
