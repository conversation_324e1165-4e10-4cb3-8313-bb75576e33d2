namespace GMCadiomJsonProvider.Factory;

/// <summary>
/// Factory for creating JSON providers.
/// </summary>
public class JsonProviderFactory : IJsonProviderFactory
{
    /// <summary>
    /// Creates a JSON provider of the specified type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to create.</param>
    /// <returns>A JSON provider instance.</returns>
    public IJsonProvider CreateProvider(JsonProviderType providerType)
    {
        return providerType switch
        {
            JsonProviderType.Microsoft => new SystemTextJsonProvider(),
            JsonProviderType.Newtonsoft => new NewtonsoftJsonProvider(),
            _ => throw new ArgumentOutOfRangeException(nameof(providerType), providerType, "Unsupported provider type.")
        };
    }

    /// <summary>
    /// Creates a Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="options">Optional configuration options.</param>
    /// <returns>A Microsoft JSON provider instance.</returns>
    public IJsonProvider CreateMicrosoftProvider(SystemTextJsonOptions? options = null)
    {
        return new SystemTextJsonProvider(options);
    }

    /// <summary>
    /// Creates a Newtonsoft.Json provider.
    /// </summary>
    /// <param name="options">Optional configuration options.</param>
    /// <returns>A Newtonsoft JSON provider instance.</returns>
    public IJsonProvider CreateNewtonsoftProvider(NewtonsoftJsonOptions? options = null)
    {
        return new NewtonsoftJsonProvider(options);
    }

    /// <summary>
    /// Creates a JSON provider with the specified configuration.
    /// </summary>
    /// <param name="options">The configuration options.</param>
    /// <returns>A JSON provider instance.</returns>
    public IJsonProvider CreateProvider(JsonProviderOptions options)
    {
        return options.ProviderType switch
        {
            JsonProviderType.Microsoft when options is SystemTextJsonOptions microsoftOptions =>
                new SystemTextJsonProvider(microsoftOptions),
            JsonProviderType.Microsoft =>
                new SystemTextJsonProvider(),
            JsonProviderType.Newtonsoft when options is NewtonsoftJsonOptions newtonsoftOptions =>
                new NewtonsoftJsonProvider(newtonsoftOptions),
            JsonProviderType.Newtonsoft =>
                new NewtonsoftJsonProvider(),
            _ => throw new ArgumentOutOfRangeException(nameof(options.ProviderType), options.ProviderType, "Unsupported provider type.")
        };
    }

    /// <summary>
    /// Creates a JSON provider using unified configuration.
    /// </summary>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>A JSON provider instance.</returns>
    public IJsonProvider CreateProvider(JsonConfiguration config)
    {
        return config.ProviderType switch
        {
            JsonProviderType.Microsoft => new SystemTextJsonProvider(JsonConfigurationMapper.ToSystemTextJsonOptions(config)),
            JsonProviderType.Newtonsoft => new NewtonsoftJsonProvider(JsonConfigurationMapper.ToNewtonsoftJsonOptions(config)),
            _ => throw new ArgumentOutOfRangeException(nameof(config.ProviderType), config.ProviderType, "Unsupported provider type.")
        };
    }

    /// <summary>
    /// Creates a JSON provider from a JSON configuration string.
    /// </summary>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>A JSON provider instance.</returns>
    public IJsonProvider CreateProviderFromJson(string jsonConfig)
    {
        var config = JsonConfiguration.FromJson(jsonConfig);
        return CreateProvider(config);
    }
}
