namespace GMCadiomJsonProvider.Builder;

/// <summary>
/// Defines the contract for building JSON providers with fluent configuration.
/// </summary>
public interface IJsonProviderBuilder
{
    /// <summary>
    /// Configures the builder to use Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure System.Text.Json options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseMicrosoft(Action<SystemTextJsonOptions>? configure = null);

    /// <summary>
    /// Configures the builder to use Newtonsoft.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure Newtonsoft.Json options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseNewtonsoft(Action<NewtonsoftJsonOptions>? configure = null);

    /// <summary>
    /// Configures the builder to use a specific provider type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to use.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder UseProvider(JsonProviderType providerType);

    /// <summary>
    /// Configures common JSON options.
    /// </summary>
    /// <param name="configure">Action to configure common options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder ConfigureOptions(Action<JsonProviderOptions> configure);

    /// <summary>
    /// Sets whether to write indented JSON.
    /// </summary>
    /// <param name="writeIndented">True to write indented JSON; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithIndentation(bool writeIndented = true);

    /// <summary>
    /// Sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    /// <param name="caseInsensitive">True for case-insensitive property names; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithCaseInsensitiveProperties(bool caseInsensitive = true);

    /// <summary>
    /// Sets whether to allow trailing commas in JSON.
    /// </summary>
    /// <param name="allowTrailingCommas">True to allow trailing commas; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithTrailingCommas(bool allowTrailingCommas = true);

    /// <summary>
    /// Sets whether to ignore null values during serialization.
    /// </summary>
    /// <param name="ignoreNullValues">True to ignore null values; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    IJsonProviderBuilder WithIgnoreNullValues(bool ignoreNullValues = true);

    /// <summary>
    /// Builds the configured JSON provider.
    /// </summary>
    /// <returns>A configured JSON provider instance.</returns>
    IJsonProvider Build();
}
